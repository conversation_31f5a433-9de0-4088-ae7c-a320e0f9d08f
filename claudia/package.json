{"name": "claudia", "private": true, "version": "0.1.0", "license": "AGPL-3.0", "type": "module", "scripts": {"predev": "bun run build:executables:current", "dev": "vite", "build": "tsc && vite build", "prebuild": "bun run build:executables:current", "build:executables": "bun run scripts/fetch-and-build.js --version=1.0.41", "build:executables:current": "bun run scripts/fetch-and-build.js current --version=1.0.41", "build:executables:linux": "bun run scripts/fetch-and-build.js linux --version=1.0.41", "build:executables:macos": "bun run scripts/fetch-and-build.js macos --version=1.0.41", "build:executables:windows": "bun run scripts/fetch-and-build.js windows --version=1.0.41", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.1.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.3", "@radix-ui/react-tooltip": "^1.1.5", "@tailwindcss/cli": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-virtual": "^3.13.10", "@tauri-apps/api": "^2.1.1", "@tauri-apps/plugin-dialog": "^2.0.2", "@tauri-apps/plugin-global-shortcut": "^2.0.0", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2.0.1", "@types/diff": "^8.0.0", "@types/react-syntax-highlighter": "^15.5.13", "@uiw/react-md-editor": "^4.0.7", "ansi-to-html": "^0.7.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "diff": "^8.0.2", "framer-motion": "^12.0.0-alpha.1", "html2canvas": "^1.4.1", "lucide-react": "^0.468.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-markdown": "^9.0.3", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.14.1", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.1.8", "zod": "^3.24.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/node": "^22.15.30", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/sharp": "^0.32.0", "@vitejs/plugin-react": "^4.3.4", "sharp": "^0.34.2", "typescript": "~5.6.2", "vite": "^6.0.3"}, "trustedDependencies": ["@parcel/watcher", "@tailwindcss/oxide"]}