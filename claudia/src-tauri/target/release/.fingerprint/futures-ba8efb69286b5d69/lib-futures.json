{"rustc": 11410426090777951712, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 18348216721672176038, "path": 6406816739198035470, "deps": [[5103565458935487, "futures_io", false, 6388778004639909585], [1811549171721445101, "futures_channel", false, 14089712051185729978], [7013762810557009322, "futures_sink", false, 13180502392998936233], [7620660491849607393, "futures_core", false, 10414768895564190698], [10629569228670356391, "futures_util", false, 7268258548428113612], [12779779637805422465, "futures_executor", false, 14957910898662114552], [16240732885093539806, "futures_task", false, 1672210603616626160]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/futures-ba8efb69286b5d69/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}