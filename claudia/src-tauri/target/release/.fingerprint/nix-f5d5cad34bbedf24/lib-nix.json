{"rustc": 11410426090777951712, "features": "[\"feature\", \"memoffset\", \"socket\", \"uio\", \"user\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"fanotify\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"syslog\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 1600181213338542824, "profile": 2040997289075261528, "path": 5288829261600788584, "deps": [[2924422107542798392, "libc", false, 17661277474283818302], [5150833351789356492, "build_script_build", false, 13615846836344297170], [7896293946984509699, "bitflags", false, 11078553524874675643], [10411997081178400487, "cfg_if", false, 1882669037194669137], [14643204177830147187, "memoffset", false, 14734118092508520738]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/nix-f5d5cad34bbedf24/dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}