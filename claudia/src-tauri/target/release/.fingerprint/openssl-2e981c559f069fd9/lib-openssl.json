{"rustc": 11410426090777951712, "features": "[\"default\"]", "declared_features": "[\"aws-lc\", \"bindgen\", \"default\", \"unstable_boringssl\", \"v101\", \"v102\", \"v110\", \"v111\", \"vendored\"]", "target": 17474193825155910204, "profile": 2040997289075261528, "path": 13127007580995444065, "deps": [[2924422107542798392, "libc", false, 17661277474283818302], [3722963349756955755, "once_cell", false, 13162431840019346836], [6635237767502169825, "foreign_types", false, 1038857867949042816], [7896293946984509699, "bitflags", false, 11078553524874675643], [8607891082156236373, "build_script_build", false, 12527431524669431135], [9070360545695802481, "ffi", false, 4897874538269394457], [10099563100786658307, "openssl_macros", false, 11213281316997216263], [10411997081178400487, "cfg_if", false, 1882669037194669137]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/openssl-2e981c559f069fd9/dep-lib-openssl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}