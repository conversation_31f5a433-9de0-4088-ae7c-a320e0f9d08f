{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2040997289075261528, "path": 9126903264641317125, "deps": [[1573238666360410412, "rand_chacha", false, 1649385639874723186], [2924422107542798392, "libc", false, 17661277474283818302], [18130209639506977569, "rand_core", false, 16150817407740875698]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rand-80227a0e788cc24c/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}