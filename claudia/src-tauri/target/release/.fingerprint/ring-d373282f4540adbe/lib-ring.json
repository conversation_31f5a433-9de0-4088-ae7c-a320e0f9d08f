{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 2040997289075261528, "path": 12899940214203965239, "deps": [[5491919304041016563, "build_script_build", false, 17968999202851185610], [8995469080876806959, "untrusted", false, 759684943889287931], [9920160576179037441, "getrandom", false, 8635928553091743112], [10411997081178400487, "cfg_if", false, 1882669037194669137]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/ring-d373282f4540adbe/dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}