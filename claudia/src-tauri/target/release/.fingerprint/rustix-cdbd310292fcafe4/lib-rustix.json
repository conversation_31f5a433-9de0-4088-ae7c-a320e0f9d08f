{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"event\", \"fs\", \"net\", \"pipe\", \"process\", \"std\", \"time\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 9298903534527576498, "path": 2968752561982266289, "deps": [[7896293946984509699, "bitflags", false, 11078553524874675643], [12053020504183902936, "build_script_build", false, 637083294402655557], [12846346674781677812, "linux_raw_sys", false, 4638392893854465904]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustix-cdbd310292fcafe4/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}