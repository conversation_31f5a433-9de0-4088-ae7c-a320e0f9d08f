{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"event\", \"fs\", \"libc-extra-traits\", \"net\", \"process\", \"std\", \"system\", \"use-libc-auxv\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 10474043801839359757, "path": 9268889790533085050, "deps": [[3430646239657634944, "build_script_build", false, 4241022154628793461], [5036304442846774733, "linux_raw_sys", false, 414441198660303399], [7896293946984509699, "bitflags", false, 11078553524874675643]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustix-ef6b449b51ff17e8/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}