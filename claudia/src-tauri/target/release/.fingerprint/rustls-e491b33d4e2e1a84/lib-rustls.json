{"rustc": 11410426090777951712, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1988539120246850232, "path": 10836963095932079118, "deps": [[2883436298747778685, "pki_types", false, 738546964366649372], [3722963349756955755, "once_cell", false, 13162431840019346836], [5491919304041016563, "ring", false, 8556823590010233794], [6528079939221783635, "zeroize", false, 13870647091252523184], [16400140949089969347, "build_script_build", false, 6254957119541303765], [17003143334332120809, "subtle", false, 7775550556162847062], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 14883399231832128610]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustls-e491b33d4e2e1a84/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}