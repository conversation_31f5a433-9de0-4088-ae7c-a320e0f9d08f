{"rustc": 11410426090777951712, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 1369601567987815722, "path": 6226465387246444211, "deps": [[3150220818285335163, "url", false, 1004319920272978744], [6913375703034175521, "build_script_build", false, 11609234339900836749], [8319709847752024821, "uuid1", false, 1016922342205198850], [9122563107207267705, "dyn_clone", false, 3830665729131024429], [9689903380558560274, "serde", false, 4636563939422253340], [14923790796823607459, "indexmap", false, 12430834055412638048], [15367738274754116744, "serde_json", false, 17366251874744960193], [16071897500792579091, "schemars_derive", false, 5947513349123793885]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/schemars-0dcd44c7c97f671b/dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}