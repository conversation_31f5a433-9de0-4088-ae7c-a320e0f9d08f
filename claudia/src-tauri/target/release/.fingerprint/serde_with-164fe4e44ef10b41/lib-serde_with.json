{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 5456902659710135487, "path": 16806303093442666591, "deps": [[6158493786865284961, "serde_with_macros", false, 18051334970365695261], [9689903380558560274, "serde", false, 2809228506888347437], [16257276029081467297, "serde_derive", false, 15601129228473774275]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/serde_with-164fe4e44ef10b41/dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}