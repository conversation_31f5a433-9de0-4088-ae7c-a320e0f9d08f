{"rustc": 11410426090777951712, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 1369601567987815722, "path": 537693465169611156, "deps": [[1988483478007900009, "unicode_ident", false, 4151785820039168623], [3060637413840920116, "proc_macro2", false, 3514116750488497425], [17990358020177143287, "quote", false, 10357232886203871319]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-d13d43fa9cebd2c9/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}