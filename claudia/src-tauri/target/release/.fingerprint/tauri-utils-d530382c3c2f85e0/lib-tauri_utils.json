{"rustc": 11410426090777951712, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 1369601567987815722, "path": 9509017727490340911, "deps": [[561782849581144631, "html5ever", false, 52525731324402994], [1200537532907108615, "url<PERSON><PERSON>n", false, 1747020259450396044], [3060637413840920116, "proc_macro2", false, 3514116750488497425], [3129130049864710036, "memchr", false, 2967712931762864294], [3150220818285335163, "url", false, 1004319920272978744], [3191507132440681679, "serde_untagged", false, 14976797817850943191], [4899080583175475170, "semver", false, 17394015473755780737], [5986029879202738730, "log", false, 9828895521571627996], [6213549728662707793, "serde_with", false, 7514256581517865747], [6262254372177975231, "kuchiki", false, 11510757164793751607], [6606131838865521726, "ctor", false, 10249423725541872444], [6913375703034175521, "schemars", false, 5849767490112681221], [7170110829644101142, "json_patch", false, 11244299970547830794], [8319709847752024821, "uuid", false, 1016922342205198850], [9010263965687315507, "http", false, 6089559547413443816], [9451456094439810778, "regex", false, 14769748993027877670], [9689903380558560274, "serde", false, 4636563939422253340], [10806645703491011684, "thiserror", false, 5304062555263570650], [11655476559277113544, "cargo_metadata", false, 7628683601759825624], [11989259058781683633, "dunce", false, 2887983695400869574], [13625485746686963219, "anyhow", false, 7030408891962274134], [14132538657330703225, "brotli", false, 1311418064288041205], [15367738274754116744, "serde_json", false, 17366251874744960193], [15609422047640926750, "toml", false, 9703728860018091372], [15622660310229662834, "walkdir", false, 17685701689507737832], [17146114186171651583, "infer", false, 12830899882450785924], [17155886227862585100, "glob", false, 18225828468021120605], [17186037756130803222, "phf", false, 6887623665414148493], [17990358020177143287, "quote", false, 10357232886203871319]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-utils-d530382c3c2f85e0/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}