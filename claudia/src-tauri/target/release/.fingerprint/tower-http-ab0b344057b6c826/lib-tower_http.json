{"rustc": 11410426090777951712, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 2040997289075261528, "path": 6097387607746806370, "deps": [[784494742817713399, "tower_service", false, 1516039252363621199], [1906322745568073236, "pin_project_lite", false, 14001242479224070053], [4121350475192885151, "iri_string", false, 12597734288014246071], [5695049318159433696, "tower", false, 14446536341044217518], [7712452662827335977, "tower_layer", false, 16646007202775337294], [7896293946984509699, "bitflags", false, 11078553524874675643], [9010263965687315507, "http", false, 13220211119272720555], [10629569228670356391, "futures_util", false, 7268258548428113612], [14084095096285906100, "http_body", false, 14878840322398786130], [16066129441945555748, "bytes", false, 885237576867296604]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tower-http-ab0b344057b6c826/dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}