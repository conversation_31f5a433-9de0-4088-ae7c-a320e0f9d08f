{"rustc": 11410426090777951712, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2040997289075261528, "path": 11551623812854106280, "deps": [[9620753569207166497, "zerovec_derive", false, 13572874191070879132], [10706449961930108323, "yoke", false, 16616274830975182507], [17046516144589451410, "zerofrom", false, 2979525499936641830]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zerovec-bf967f8eef4dec1a/dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}