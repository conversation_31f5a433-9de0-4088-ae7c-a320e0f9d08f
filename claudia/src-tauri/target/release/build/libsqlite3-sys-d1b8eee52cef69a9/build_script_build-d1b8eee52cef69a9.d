/usr/XML/github/claudia/claudia/src-tauri/target/release/build/libsqlite3-sys-d1b8eee52cef69a9/build_script_build-d1b8eee52cef69a9.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/libsqlite3-sys-0.30.1/build.rs

/usr/XML/github/claudia/claudia/src-tauri/target/release/build/libsqlite3-sys-d1b8eee52cef69a9/build_script_build-d1b8eee52cef69a9: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/libsqlite3-sys-0.30.1/build.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/libsqlite3-sys-0.30.1/build.rs:

# env-dep:CARGO_MANIFEST_DIR=/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/libsqlite3-sys-0.30.1
