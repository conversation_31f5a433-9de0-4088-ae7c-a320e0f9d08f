cargo:rustc-check-cfg=cfg(osslconf, values("OPENSSL_NO_OCB", "OPENSSL_NO_SM4", "OPENSSL_NO_SEED", "OPENSSL_NO_CHACHA", "OPENSSL_NO_CAST", "OPENSSL_NO_IDEA", "OPENSSL_NO_CAMELLIA", "OPENSSL_NO_RC4", "OPENSSL_NO_BF", "OPENSSL_NO_PSK", "OPENSSL_NO_DEPRECATED_3_0", "OPENSSL_NO_SCRYPT", "OPENSSL_NO_SM3", "OPENSSL_NO_RMD160", "OPENSSL_NO_EC2M", "OPENSSL_NO_OCSP", "OPENSSL_NO_CMS", "OPENSSL_NO_EC", "OPENSSL_NO_ARGON2", "OPENSSL_NO_RC2"))
cargo:rustc-check-cfg=cfg(libressl)
cargo:rustc-check-cfg=cfg(boringssl)
cargo:rustc-check-cfg=cfg(awslc)
cargo:rustc-check-cfg=cfg(libressl250)
cargo:rustc-check-cfg=cfg(libressl251)
cargo:rustc-check-cfg=cfg(libressl261)
cargo:rustc-check-cfg=cfg(libressl270)
cargo:rustc-check-cfg=cfg(libressl271)
cargo:rustc-check-cfg=cfg(libressl273)
cargo:rustc-check-cfg=cfg(libressl280)
cargo:rustc-check-cfg=cfg(libressl291)
cargo:rustc-check-cfg=cfg(libressl310)
cargo:rustc-check-cfg=cfg(libressl321)
cargo:rustc-check-cfg=cfg(libressl332)
cargo:rustc-check-cfg=cfg(libressl340)
cargo:rustc-check-cfg=cfg(libressl350)
cargo:rustc-check-cfg=cfg(libressl360)
cargo:rustc-check-cfg=cfg(libressl361)
cargo:rustc-check-cfg=cfg(libressl370)
cargo:rustc-check-cfg=cfg(libressl380)
cargo:rustc-check-cfg=cfg(libressl382)
cargo:rustc-check-cfg=cfg(libressl390)
cargo:rustc-check-cfg=cfg(libressl400)
cargo:rustc-check-cfg=cfg(libressl410)
cargo:rustc-check-cfg=cfg(ossl101)
cargo:rustc-check-cfg=cfg(ossl102)
cargo:rustc-check-cfg=cfg(ossl110)
cargo:rustc-check-cfg=cfg(ossl110g)
cargo:rustc-check-cfg=cfg(ossl110h)
cargo:rustc-check-cfg=cfg(ossl111)
cargo:rustc-check-cfg=cfg(ossl111d)
cargo:rustc-check-cfg=cfg(ossl300)
cargo:rustc-check-cfg=cfg(ossl310)
cargo:rustc-check-cfg=cfg(ossl320)
cargo:rustc-check-cfg=cfg(ossl330)
cargo:rustc-cfg=osslconf="OPENSSL_NO_IDEA"
cargo:rustc-cfg=osslconf="OPENSSL_NO_SSL3_METHOD"
cargo:rustc-cfg=ossl101
cargo:rustc-cfg=ossl102
cargo:rustc-cfg=ossl110
cargo:rustc-cfg=ossl110g
cargo:rustc-cfg=ossl110h
cargo:rustc-cfg=ossl111
cargo:rustc-cfg=ossl111d
