/usr/XML/github/claudia/claudia/src-tauri/target/release/deps/async_broadcast-b81185d5ed83ef1d.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-broadcast-0.7.2/src/lib.rs

/usr/XML/github/claudia/claudia/src-tauri/target/release/deps/libasync_broadcast-b81185d5ed83ef1d.rlib: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-broadcast-0.7.2/src/lib.rs

/usr/XML/github/claudia/claudia/src-tauri/target/release/deps/libasync_broadcast-b81185d5ed83ef1d.rmeta: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-broadcast-0.7.2/src/lib.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-broadcast-0.7.2/src/lib.rs:
