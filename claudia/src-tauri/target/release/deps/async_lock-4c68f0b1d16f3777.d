/usr/XML/github/claudia/claudia/src-tauri/target/release/deps/async_lock-4c68f0b1d16f3777.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/barrier.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/mutex.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/once_cell.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/rwlock.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/rwlock/futures.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/rwlock/raw.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/semaphore.rs

/usr/XML/github/claudia/claudia/src-tauri/target/release/deps/libasync_lock-4c68f0b1d16f3777.rlib: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/barrier.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/mutex.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/once_cell.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/rwlock.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/rwlock/futures.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/rwlock/raw.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/semaphore.rs

/usr/XML/github/claudia/claudia/src-tauri/target/release/deps/libasync_lock-4c68f0b1d16f3777.rmeta: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/barrier.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/mutex.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/once_cell.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/rwlock.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/rwlock/futures.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/rwlock/raw.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/semaphore.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/lib.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/barrier.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/mutex.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/once_cell.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/rwlock.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/rwlock/futures.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/rwlock/raw.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/async-lock-3.4.0/src/semaphore.rs:
