/usr/XML/github/claudia/claudia/src-tauri/target/release/deps/html5ever-f16256323bda2f90.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/macros.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/util/str.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/driver.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/serialize/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/char_ref/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/interface.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/states.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/tag_sets.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/data.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/types.rs /usr/XML/github/claudia/claudia/src-tauri/target/release/build/html5ever-e1ce54b82ef7642e/out/rules.rs

/usr/XML/github/claudia/claudia/src-tauri/target/release/deps/libhtml5ever-f16256323bda2f90.rlib: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/macros.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/util/str.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/driver.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/serialize/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/char_ref/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/interface.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/states.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/tag_sets.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/data.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/types.rs /usr/XML/github/claudia/claudia/src-tauri/target/release/build/html5ever-e1ce54b82ef7642e/out/rules.rs

/usr/XML/github/claudia/claudia/src-tauri/target/release/deps/libhtml5ever-f16256323bda2f90.rmeta: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/macros.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/util/str.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/driver.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/serialize/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/char_ref/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/interface.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/states.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/tag_sets.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/data.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/types.rs /usr/XML/github/claudia/claudia/src-tauri/target/release/build/html5ever-e1ce54b82ef7642e/out/rules.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/lib.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/macros.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/util/str.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/driver.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/serialize/mod.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/mod.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/char_ref/mod.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/interface.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tokenizer/states.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/mod.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/tag_sets.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/data.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/html5ever-0.26.0/src/tree_builder/types.rs:
/usr/XML/github/claudia/claudia/src-tauri/target/release/build/html5ever-e1ce54b82ef7642e/out/rules.rs:

# env-dep:OUT_DIR=/usr/XML/github/claudia/claudia/src-tauri/target/release/build/html5ever-e1ce54b82ef7642e/out
