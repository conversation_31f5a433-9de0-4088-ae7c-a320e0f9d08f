/usr/XML/github/claudia/claudia/src-tauri/target/release/deps/markup5ever-df94cac2ad8e096a.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/data/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/interface/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/interface/tree_builder.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/serialize.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/util/buffer_queue.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/util/smallcharset.rs /usr/XML/github/claudia/claudia/src-tauri/target/release/build/markup5ever-7fbb0081b62b2bb1/out/generated.rs /usr/XML/github/claudia/claudia/src-tauri/target/release/build/markup5ever-7fbb0081b62b2bb1/out/named_entities.rs

/usr/XML/github/claudia/claudia/src-tauri/target/release/deps/libmarkup5ever-df94cac2ad8e096a.rlib: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/data/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/interface/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/interface/tree_builder.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/serialize.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/util/buffer_queue.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/util/smallcharset.rs /usr/XML/github/claudia/claudia/src-tauri/target/release/build/markup5ever-7fbb0081b62b2bb1/out/generated.rs /usr/XML/github/claudia/claudia/src-tauri/target/release/build/markup5ever-7fbb0081b62b2bb1/out/named_entities.rs

/usr/XML/github/claudia/claudia/src-tauri/target/release/deps/libmarkup5ever-df94cac2ad8e096a.rmeta: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/data/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/interface/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/interface/tree_builder.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/serialize.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/util/buffer_queue.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/util/smallcharset.rs /usr/XML/github/claudia/claudia/src-tauri/target/release/build/markup5ever-7fbb0081b62b2bb1/out/generated.rs /usr/XML/github/claudia/claudia/src-tauri/target/release/build/markup5ever-7fbb0081b62b2bb1/out/named_entities.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/lib.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/data/mod.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/interface/mod.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/interface/tree_builder.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/serialize.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/util/buffer_queue.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.11.0/util/smallcharset.rs:
/usr/XML/github/claudia/claudia/src-tauri/target/release/build/markup5ever-7fbb0081b62b2bb1/out/generated.rs:
/usr/XML/github/claudia/claudia/src-tauri/target/release/build/markup5ever-7fbb0081b62b2bb1/out/named_entities.rs:

# env-dep:OUT_DIR=/usr/XML/github/claudia/claudia/src-tauri/target/release/build/markup5ever-7fbb0081b62b2bb1/out
